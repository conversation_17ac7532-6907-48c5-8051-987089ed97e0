import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import os

sns.set_style("whitegrid")


def data_analysis_plot(j):
    # 检查文件是否存在
    filename = f"train_FD00{j}.txt"
    if not os.path.exists(filename):
        print(f"文件 {filename} 不存在，跳过...")
        return

    print(f"正在处理数据集 FD00{j}...")
    # 读取原始训练集数据
    raw1 = np.loadtxt(filename)
    df1 = pd.DataFrame(raw1, columns=['unit', 'cycles', 'operational setting 1', 'operational setting 2',
                                      'operational setting 3'] + ['sensor measurement' + str(i) for i in
                                                                  range(1, 22)])
    # print(df1.iloc[:, 5:].describe())
    # 绘制传感器读数变化曲线
    plt.figure(1, figsize=(15, 9))
    plt.title('dataset' + str(j) + 'graph', fontsize=15)
    for i in range(1, 22):
        plt.subplot(5, 5, i)
        plt.title(i, fontsize=15)
        df1.iloc[:, i + 4].plot()
    # 保存图片而不是显示
    plt.tight_layout()
    plt.savefig(f'dataset_FD00{j}_sensor_plots.png', dpi=300, bbox_inches='tight')
    plt.close()
    print(f"传感器变化曲线已保存为: dataset_FD00{j}_sensor_plots.png")

    plt.figure(2, figsize=(15, 9))
    plt.title('dataset' + str(j) + 'histogram', fontsize=15)
    for i in range(1, 22):
        plt.subplot(5, 5, i)
        plt.title(i, fontsize=15)
        sns.histplot(df1.iloc[:, i + 4], kde=True)
    # 保存图片而不是显示
    plt.tight_layout()
    plt.savefig(f'dataset_FD00{j}_histograms.png', dpi=300, bbox_inches='tight')
    plt.close()
    print(f"传感器直方图已保存为: dataset_FD00{j}_histograms.png")


if __name__ == '__main__':
    # 只处理FD001数据集（可以根据需要修改）
    data_analysis_plot(1)
    # data_analysis_plot(2)  # 如需要可以取消注释
    # data_analysis_plot(3)  # 如需要可以取消注释
    # data_analysis_plot(4)  # 如需要可以取消注释
